#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试频率错误处理器的点击功能
"""

import time
import logging
from modules.frequency_error_handler import FrequencyErrorHandler, ErrorDetectionResult, WindowInfo

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_frequency_handler_click():
    """测试频率错误处理器的点击功能"""
    logger.info("🚀 开始测试频率错误处理器点击功能...")
    
    try:
        # 创建频率错误处理器
        handler = FrequencyErrorHandler()
        logger.info("✅ 频率错误处理器创建成功")
        
        # 等待用户准备
        logger.info("⏰ 5秒后开始测试，请确保添加朋友窗口已打开...")
        time.sleep(5)
        
        # 测试1: 直接调用强制点击方法
        logger.info("🧪 测试1: 直接调用强制点击添加朋友确定按钮...")
        result1 = handler._force_click_add_friend_ok_button()
        logger.info(f"测试1结果: {result1}")
        
        time.sleep(2)
        
        # 测试2: 调用主要的点击方法
        logger.info("🧪 测试2: 调用主要的点击错误对话框方法...")
        result2 = handler._click_error_dialog_ok_button()
        logger.info(f"测试2结果: {result2}")
        
        time.sleep(2)
        
        # 测试3: 查找错误对话框窗口
        logger.info("🧪 测试3: 查找错误对话框窗口...")
        error_dialogs = handler._find_error_dialog_windows()
        logger.info(f"找到的错误对话框数量: {len(error_dialogs)}")
        for i, dialog in enumerate(error_dialogs):
            logger.info(f"  对话框{i+1}: {dialog.title} ({dialog.class_name}) 大小:{dialog.rect[2]-dialog.rect[0]}x{dialog.rect[3]-dialog.rect[1]}")
        
        # 测试4: 如果找到对话框，尝试点击
        if error_dialogs:
            logger.info("🧪 测试4: 尝试点击找到的对话框...")
            for dialog in error_dialogs:
                logger.info(f"尝试点击对话框: {dialog.title}")
                result4 = handler._try_click_ok_in_window(dialog)
                logger.info(f"点击结果: {result4}")
                if result4:
                    break
        
        # 测试5: 固定坐标点击
        logger.info("🧪 测试5: 固定坐标点击...")
        result5 = handler._click_ok_by_fixed_coordinates()
        logger.info(f"测试5结果: {result5}")
        
        logger.info("🎉 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        logger.error(traceback.format_exc())

def test_window_detection():
    """测试窗口检测功能"""
    logger.info("🔍 开始测试窗口检测功能...")
    
    try:
        handler = FrequencyErrorHandler()
        
        # 检测所有可能的错误对话框
        error_dialogs = handler._find_error_dialog_windows()
        logger.info(f"📊 检测到 {len(error_dialogs)} 个可能的错误对话框")
        
        for i, dialog in enumerate(error_dialogs):
            logger.info(f"对话框 {i+1}:")
            logger.info(f"  标题: {dialog.title}")
            logger.info(f"  类名: {dialog.class_name}")
            logger.info(f"  大小: {dialog.rect[2]-dialog.rect[0]} x {dialog.rect[3]-dialog.rect[1]}")
            logger.info(f"  位置: ({dialog.rect[0]}, {dialog.rect[1]}) 到 ({dialog.rect[2]}, {dialog.rect[3]})")
            logger.info(f"  可见: {dialog.is_visible}")
            logger.info(f"  启用: {dialog.is_enabled}")
            logger.info("---")
        
    except Exception as e:
        logger.error(f"❌ 窗口检测异常: {e}")

if __name__ == "__main__":
    logger.info("🚀 启动频率错误处理器测试程序")
    
    # 测试窗口检测
    test_window_detection()
    
    print("\n" + "="*50)
    
    # 测试点击功能
    test_frequency_handler_click()
    
    logger.info("🏁 测试程序结束")
