#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的点击测试
"""

import time
import pyautogui
import win32api
import win32con

def simple_click_test():
    """简单的坐标点击测试"""
    print("🎯 开始简单坐标点击测试...")
    print("⏰ 3秒后开始点击坐标 (165, 220)...")
    time.sleep(3)
    
    try:
        # 方法1: pyautogui点击
        print("🖱️ 使用 pyautogui 点击...")
        pyautogui.click(165, 220)
        time.sleep(1)
        
        # 方法2: win32api点击
        print("🖱️ 使用 win32api 点击...")
        win32api.SetCursorPos((165, 220))
        time.sleep(0.2)
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        time.sleep(0.1)
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        
        print("✅ 点击完成")
        
    except Exception as e:
        print(f"❌ 点击失败: {e}")

if __name__ == "__main__":
    simple_click_test()
