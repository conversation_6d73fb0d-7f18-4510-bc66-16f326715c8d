#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的频率错误处理器
"""

import sys
import os
import time
import logging

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_simplified_click():
    """测试简化后的点击方法"""
    print("🚀 测试简化后的频率错误处理器...")
    
    try:
        # 导入模块
        from modules.frequency_error_handler import FrequencyErrorHandler
        print("✅ 模块导入成功")
        
        # 创建处理器
        handler = FrequencyErrorHandler()
        print("✅ 处理器创建成功")
        
        print("⏰ 3秒后开始测试点击...")
        time.sleep(3)
        
        # 直接调用简化后的点击方法
        print("🎯 调用简化后的 _click_error_dialog_ok_button 方法...")
        result = handler._click_error_dialog_ok_button()
        print(f"📊 点击结果: {result}")
        
        if result:
            print("✅ 简化版点击成功！")
        else:
            print("❌ 简化版点击失败")
            
        # 测试直接调用固定坐标方法
        print("\n🎯 直接测试 _force_click_add_friend_ok_button 方法...")
        result2 = handler._force_click_add_friend_ok_button()
        print(f"📊 固定坐标点击结果: {result2}")
        
        if result2:
            print("✅ 固定坐标点击成功！")
        else:
            print("❌ 固定坐标点击失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simplified_click()
