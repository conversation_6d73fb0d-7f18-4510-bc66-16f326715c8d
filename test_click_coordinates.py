#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标点击功能
用于验证添加朋友窗口确定按钮的坐标点击是否正常工作
"""

import time
import pyautogui
import win32api
import win32con
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_click_coordinates():
    """测试点击添加朋友窗口确定按钮坐标"""
    logger.info("🎯 开始测试点击坐标...")
    
    # 添加朋友窗口确定按钮的坐标
    test_positions = [
        (165, 220),  # 主要坐标
        (165, 200),  # 稍微偏上
        (165, 240),  # 稍微偏下
        (180, 220),  # 稍微偏右
        (150, 220),  # 稍微偏左
    ]
    
    logger.info("⏰ 5秒后开始点击测试，请确保添加朋友窗口已打开...")
    time.sleep(5)
    
    for i, pos in enumerate(test_positions, 1):
        logger.info(f"🖱️ 测试 {i}/5: 点击坐标 {pos}")
        
        try:
            # 方法1: pyautogui点击
            logger.info(f"   方法1: pyautogui.click{pos}")
            pyautogui.click(pos[0], pos[1])
            time.sleep(1.0)
            
            # 方法2: win32api点击
            logger.info(f"   方法2: win32api点击{pos}")
            win32api.SetCursorPos(pos)
            time.sleep(0.2)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(0.1)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            time.sleep(1.0)
            
            logger.info(f"✅ 坐标 {pos} 点击完成")
            
            # 询问用户是否成功
            print(f"\n坐标 {pos} 点击是否成功？(y/n): ", end="")
            user_input = input().strip().lower()
            if user_input == 'y':
                logger.info(f"✅ 用户确认坐标 {pos} 点击成功！")
                return pos
            else:
                logger.info(f"❌ 用户确认坐标 {pos} 点击失败")
                
        except Exception as e:
            logger.error(f"❌ 点击坐标 {pos} 异常: {e}")
        
        # 等待下一次测试
        if i < len(test_positions):
            logger.info("⏰ 3秒后进行下一次测试...")
            time.sleep(3)
    
    logger.info("❌ 所有坐标测试完成，未找到有效坐标")
    return None

def test_screen_info():
    """测试屏幕信息"""
    logger.info("📺 获取屏幕信息...")
    
    try:
        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        logger.info(f"屏幕分辨率: {screen_width} x {screen_height}")
        
        # 获取当前鼠标位置
        mouse_x, mouse_y = pyautogui.position()
        logger.info(f"当前鼠标位置: ({mouse_x}, {mouse_y})")
        
        return screen_width, screen_height
        
    except Exception as e:
        logger.error(f"❌ 获取屏幕信息异常: {e}")
        return None, None

if __name__ == "__main__":
    logger.info("🚀 启动坐标点击测试程序")
    
    # 测试屏幕信息
    screen_width, screen_height = test_screen_info()
    
    # 测试坐标点击
    successful_pos = test_click_coordinates()
    
    if successful_pos:
        logger.info(f"🎉 找到有效坐标: {successful_pos}")
        logger.info(f"建议在代码中使用此坐标: ({successful_pos[0]}, {successful_pos[1]})")
    else:
        logger.info("❌ 未找到有效坐标，请检查窗口位置或手动确定坐标")
    
    logger.info("🏁 测试程序结束")
